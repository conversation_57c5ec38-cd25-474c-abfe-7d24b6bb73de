# Quotese API模式切换和测试指南

## 📋 概述

本指南说明如何在本地Quotese项目中切换使用本地API和生产API，以及如何测试两种模式的兼容性。

## 🔧 配置架构

### API端点配置

| 环境 | REST API | GraphQL API | 数据库 |
|------|----------|-------------|--------|
| **本地开发** | `http://127.0.0.1:8000/api/` | `http://127.0.0.1:8000/graphql/` | SQLite |
| **生产环境** | `https://api.quotese.com/api/` | `https://api.quotese.com/graphql/` | MySQL |

### CORS配置状态
- ✅ **生产API**: `CORS_ALLOW_ALL_ORIGINS = True` - 允许localhost访问
- ✅ **本地API**: `CORS_ALLOW_ALL_ORIGINS = True` - 开发环境配置

## 🚀 快速开始

### 1. 启动本地环境
```bash
# 在项目根目录下
./start_local.sh
```

访问地址：
- 🌐 **网站首页**: http://localhost:8083
- 🧪 **API测试页面**: http://localhost:8083/test-production-api.html
- 🔧 **Django管理**: http://localhost:8000/admin

### 2. API模式切换

#### 方法一：使用脚本（推荐）
```bash
# 查看帮助
./switch-api-mode.sh help

# 切换到生产API
./switch-api-mode.sh production

# 切换到本地API  
./switch-api-mode.sh local

# 测试连接
./switch-api-mode.sh test
```

#### 方法二：浏览器控制台
```javascript
// 切换到生产API
QuoteseAPIMode.useProductionAPI()

// 切换到本地API
QuoteseAPIMode.useLocalAPI()

// 查看当前模式
QuoteseAPIMode.getCurrentMode()

// 测试连接
QuoteseAPIMode.testConnection()
```

#### 方法三：URL参数（临时）
```
# 强制使用生产API（仅当前页面）
http://localhost:8083/?use-production-api=true
```

## 🧪 测试流程

### 1. 基础连接测试

**步骤：**
1. 打开 http://localhost:8083/test-production-api.html
2. 点击"测试API连接"按钮
3. 查看连接状态

**预期结果：**
- ✅ REST API连接正常 (HTTP 200)
- ✅ GraphQL API连接正常 (HTTP 200)

### 2. 完整功能测试

**测试页面：**
- 📄 **首页**: http://localhost:8083/
- 📂 **分类页**: http://localhost:8083/categories/
- 👥 **作者页**: http://localhost:8083/authors/
- 📚 **来源页**: http://localhost:8083/sources/
- 🔍 **搜索页**: http://localhost:8083/search.html

**测试步骤：**
1. 切换到生产API模式
2. 访问各个页面
3. 验证数据加载正常
4. 测试热门模块功能
5. 验证页面跳转和导航

### 3. 性能对比测试

**优化导航测试：**
```javascript
// 在浏览器控制台中运行
// 测试热门模块跳转性能
window.location.href = 'http://localhost:8083/categories/life/?perf-test=true'
```

**预期结果：**
- 🚀 优化路径：< 5ms
- 📊 标准路径：50-250ms
- 📈 性能提升：40-50倍

## 📊 测试检查清单

### ✅ 基础功能测试

- [ ] **API连接测试**
  - [ ] REST API响应正常
  - [ ] GraphQL API响应正常
  - [ ] CORS头部正确设置

- [ ] **数据加载测试**
  - [ ] 首页数据正常显示
  - [ ] 分类列表加载完整
  - [ ] 作者列表加载完整
  - [ ] 来源列表加载完整

- [ ] **页面导航测试**
  - [ ] 语义化URL正常工作
  - [ ] 页面间跳转无错误
  - [ ] 面包屑导航正确

### ✅ 高级功能测试

- [ ] **搜索功能**
  - [ ] 关键词搜索正常
  - [ ] 搜索结果显示正确
  - [ ] 分页功能正常

- [ ] **热门模块**
  - [ ] Popular Categories加载
  - [ ] Popular Authors加载
  - [ ] Popular Sources加载
  - [ ] 优化导航功能正常

- [ ] **详情页面**
  - [ ] 名言详情页正常
  - [ ] 作者详情页正常
  - [ ] 来源详情页正常
  - [ ] 分类详情页正常

## 🔍 故障排除

### 常见问题

**1. 生产API连接失败**
```bash
# 检查网络连接
curl -I https://api.quotese.com/api/

# 检查CORS设置
curl -H "Origin: http://localhost:8083" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS https://api.quotese.com/api/
```

**2. 本地API连接失败**
```bash
# 检查Django服务器状态
curl -I http://127.0.0.1:8000/api/

# 查看Django日志
tail -f logs/django.log
```

**3. 配置切换不生效**
```javascript
// 清除localStorage
localStorage.removeItem('quotese-use-production-api')

// 强制刷新页面
location.reload(true)
```

### 调试工具

**浏览器控制台命令：**
```javascript
// 查看当前配置
console.log(window.AppConfig)

// 查看实体缓存状态
console.log(window.entityCache)

// 查看优化导航状态
console.log(typeof window.navigateToEntityWithId)

// 测试GraphQL查询
fetch(window.AppConfig.graphqlEndpoint, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        query: '{ allCategories { id name slug } }'
    })
}).then(r => r.json()).then(console.log)
```

## 📝 配置文件说明

### frontend/js/config.js
```javascript
// 支持的切换方式：
// 1. URL参数: ?use-production-api=true
// 2. localStorage: quotese-use-production-api=true
// 3. 域名检测: 自动识别环境
```

### 环境变量
```bash
# 生产环境
DJANGO_SETTINGS_MODULE=quotes_admin.settings_prod

# 本地环境  
DJANGO_SETTINGS_MODULE=quotes_admin.settings_local
```

## 🎯 最佳实践

### 开发流程建议

1. **本地开发**: 使用本地API进行功能开发
2. **集成测试**: 切换到生产API验证兼容性
3. **性能测试**: 对比两种模式的性能差异
4. **部署前验证**: 确保生产API功能完整

### 注意事项

- 🔒 **安全**: 生产API可能有访问限制
- 📊 **数据**: 生产环境数据量更大，加载时间可能更长
- 🚀 **性能**: 网络延迟会影响生产API响应时间
- 🔄 **同步**: 确保本地代码与生产环境兼容

---

**🎉 配置完成！现在您可以灵活地在本地API和生产API之间切换进行测试了。**
